"use client";

import { useState, useCallback } from "react";
import { useSession } from "@/lib/auth-client";
import { usePlanBoxOpenStore } from "@/store/usePlanBoxOpenStore";
import { useSignInBoxOpenStore } from "@/store/useSignInBoxOpenStore";
import { useUserStore } from "@/store/useUserStore";
import { useDropzone } from "react-dropzone";
import { downloadImageFromBase64, downloadImageFromBase64WithWatermark, imageUrlToBase64 } from "@/lib/file/utils-file";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Card } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { X, Download, History, SparklesIcon, CrownIcon, DownloadIcon } from "lucide-react";
import { toast } from "sonner";
import { AuthError, Credits402Error, handleError, IgnoreError } from "@/@types/error";
import { IMAGE_SIZE_LIMIT, IMAGE_SIZE_LIMIT_EXCEED_MESSAGE, OSS_URL_HOST } from "@/lib/constants";
import { uploadFile } from "@/lib/file/upload-file";
import { cn } from "@/lib/utils";
import { ofetch } from "ofetch";
import { EVENT_EDIT_IMAGE_WITH_TOOL, trackGTMEvent } from "@/lib/track-events";
import { SubmitButton } from "@/components/ui/custom/submit-button";
import { ImageComparisonSlider } from "@/components/ui/custom/image-comparison-slider";

interface HistoryImage {
	id: string;
	url: string;
	type: "uploaded" | "generated";
	timestamp: number;
}

// 示例图片和对应的prompt
const exampleData = [
	{
		image: `${OSS_URL_HOST}/mkt/pages/tools/remove-people-from-photos/try-1.jpg`,
		prompt: "Remove all people from the image, keep the rest unchanged",
	},
	{
		image: `${OSS_URL_HOST}/mkt/pages/tools/remove-people-from-photos/try-2.jpg`,
		prompt: "Remove the person in the background, keep the rest unchanged",
	},
	{
		image: `${OSS_URL_HOST}/mkt/pages/tools/remove-people-from-photos/try-3.jpg`,
		prompt: "Remove the photographer, keep the rest unchanged",
	},
];

export default function RemovePeopleClient() {
	const { data: session } = useSession();
	const { user, refreshUser, hasPaid: userHasPaid } = useUserStore();
	const { setSignInBoxOpen } = useSignInBoxOpenStore();
	const { setPlanBoxOpen } = usePlanBoxOpenStore();

	// 状态管理
	const [uploadingImage, setUploadingImage] = useState(false);
	const [uploadedImage, setUploadedImage] = useState<string | null>(null);
	const [prompt, setPrompt] = useState("");

	const [generatedImage, setGeneratedImage] = useState<string | null>(null);
	const [isGenerating, setIsGenerating] = useState(false);

	const [historyImages, setHistoryImages] = useState<HistoryImage[]>([]);
	const [showHistoryDialog, setShowHistoryDialog] = useState(false);
	const [hasEverUploaded, setHasEverUploaded] = useState(false); // 追踪是否曾经上传过图片

	// 添加图片到历史记录
	const addToHistory = useCallback((url: string, type: "uploaded" | "generated") => {
		const newImage: HistoryImage = {
			id: Date.now().toString(),
			url,
			type,
			timestamp: Date.now(),
		};
		setHistoryImages((prev) => [newImage, ...prev]);
	}, []);

	// 处理文件上传
	const handleImageUpload = async (file: File) => {
		if (!session) {
			setSignInBoxOpen(true);
			return;
		}

		if (file.size > IMAGE_SIZE_LIMIT) {
			toast.warning(IMAGE_SIZE_LIMIT_EXCEED_MESSAGE);
			return;
		}

		try {
			setUploadingImage(true);
			const { file_url } = await uploadFile(file);
			setUploadedImage(file_url);
			setHasEverUploaded(true); // 标记已经上传过图片
			addToHistory(file_url, "uploaded");
		} catch (error: any) {
			console.error("Failed to upload image:", error.message);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
				return;
			}
			toast.error(`Upload image failed: ${error.message}`);
		} finally {
			setUploadingImage(false);
		}
	};

	const {
		getRootProps,
		getInputProps,
		open: openDropzone,
	} = useDropzone({
		noClick: true,
		multiple: false,
		accept: {
			"image/jpeg": [],
			"image/png": [],
			"image/webp": [],
		},
		maxFiles: 1,
		onError: console.error,
		onDrop: async (acceptedFiles, fileRejections) => {
			if (uploadingImage) return;
			if (fileRejections.length > 0) {
				const message = fileRejections.at(0)?.errors.at(0)?.message;
				toast.error(message || "File rejected");
				return;
			}
			const file = acceptedFiles[0];
			console.log("file", file);
			if (file) {
				await handleImageUpload(file);
			}
		},
	});

	// 处理示例图片选择
	const handleExampleImageSelect = async (imageUrl: string, prompt: string) => {
		setUploadedImage(imageUrl);
		setPrompt(prompt); // 设置对应的prompt
		setHasEverUploaded(true); // 标记已经上传过图片
	};

	// 处理历史图片选择
	const handleHistoryImageSelect = (imageUrl: string) => {
		setUploadedImage(imageUrl);
	};

	// 生成图片
	const handleGenerate = async () => {
		if (isGenerating) return;
		if (!session) {
			setSignInBoxOpen(true);
			return;
		}

		if (!uploadedImage || !prompt.trim()) {
			toast.error("Please upload an image and enter a prompt");
			return;
		}

		trackGTMEvent(EVENT_EDIT_IMAGE_WITH_TOOL, {
			membership_level: user?.membershipLevel,
			tool: "remove-people-from-photos",
		});

		try {
			setIsGenerating(true);
			const { status, message, resultUrl } = await ofetch("/api/v1/image/remove-object", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: {
					prompt: prompt.trim(),
					image: uploadedImage,
					tool: "remove-people-from-photos",
				},
			});
			handleError(status, message);
			refreshUser();

			if (resultUrl) {
				setGeneratedImage(resultUrl);
				addToHistory(resultUrl, "generated");
				toast.success("Image generated successfully!");
			} else {
				throw new Error("No result image received");
			}
		} catch (error: any) {
			console.error("Generation failed:", error);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
				return;
			}
			if (error instanceof IgnoreError) {
				return;
			}
			if (error instanceof Credits402Error) {
				toast.warning("You do not have enough credits.", {
					action: {
						label: "Get more",
						onClick: () => setPlanBoxOpen(true),
					},
				});
				return;
			}
			toast.error(error.message || "Generate failed.");
		} finally {
			setIsGenerating(false);
		}
	};

	// 下载功能
	const handleDownload = async (imageUrl: string) => {
		try {
			const base64 = await imageUrlToBase64(imageUrl);
			if (userHasPaid) {
				await downloadImageFromBase64(base64, "removed-people");
			} else {
				await downloadImageFromBase64WithWatermark(base64, "removed-people");
			}
		} catch (error) {
			console.error("Download failed:", error);
			toast.error("Download failed");
		}
	};

	const handleDownloadWithoutWatermark = () => {
		setPlanBoxOpen(true);
	};

	// 删除上传的图片
	const handleRemoveImage = () => {
		setUploadedImage(null);
		setGeneratedImage(null);
		// 注意：不重置hasEverUploaded，保持非初始状态
	};

	// 将生成的图片作为输入
	const handleUseAsInput = (imageUrl: string) => {
		setUploadedImage(imageUrl);
		setGeneratedImage(null);
		addToHistory(imageUrl, "uploaded");
	};

	return (
		<div className="mx-auto w-full">
			<div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:gap-8">
				{/* 左侧 */}
				<div className="space-y-6">
					{!hasEverUploaded ? (
						// 初始状态 - 展示产品效果图片对比滑块
						<ImageComparisonSlider
							beforeImage={`${OSS_URL_HOST}/mkt/pages/tools/remove-people-from-photos/demo-before.webp`}
							beforeImageAlt="Remove people from photo demo before"
							afterImage={`${OSS_URL_HOST}/mkt/pages/tools/remove-people-from-photos/demo-after.webp`}
							afterImageAlt="Remove people from photo demo after"
						/>
					) : (
						// 非初始状态
						<div className="bg-muted space-y-4 rounded-2xl p-4 md:p-6">
							{/* 上传图片区域 */}
							<div className="space-y-2">
								<div className="flex items-center justify-between">
									<Label htmlFor="upload-image" className="text-sm font-medium text-gray-900">
										Upload Image
									</Label>
									{historyImages.length > 0 && (
										<Dialog open={showHistoryDialog} onOpenChange={setShowHistoryDialog}>
											<DialogTrigger asChild>
												<Button variant="secondary" size="sm" className="bg-input hover:bg-input/80 text-xs">
													<History className="size-3.5" />
													Select from history
												</Button>
											</DialogTrigger>
											<DialogContent className="px-0 py-6 sm:max-w-2xl">
												<DialogHeader className="px-6">
													<DialogTitle>Image History</DialogTitle>
												</DialogHeader>
												<div className="grid max-h-96 min-h-96 grid-cols-2 gap-3 overflow-y-auto px-6 md:grid-cols-4">
													{historyImages.map((historyImage) => (
														<div
															key={historyImage.id}
															className="group relative aspect-square cursor-pointer rounded bg-gray-200 transition-opacity hover:opacity-80"
															onClick={() => {
																handleHistoryImageSelect(historyImage.url);
																setShowHistoryDialog(false);
															}}
														>
															<img src={historyImage.url} alt="History" className="h-full w-full rounded object-cover" />
														</div>
													))}
												</div>
											</DialogContent>
										</Dialog>
									)}
								</div>
								{uploadedImage ? (
									<div className="relative h-[200px] overflow-hidden rounded-lg">
										<img src={uploadedImage} alt="Uploaded" className="h-full w-full object-cover" />
										<Button
											variant="destructive"
											size="icon"
											className="absolute top-2 right-2 size-7 rounded-full"
											onClick={handleRemoveImage}
										>
											<X className="h-4 w-4" />
										</Button>
									</div>
								) : (
									<div
										{...getRootProps()}
										className={cn(
											"flex max-h-[200px] min-h-[200px] items-center justify-center rounded-lg border-[1.5px] border-dashed bg-white p-6 text-center transition-colors",
											uploadingImage ? "cursor-not-allowed" : "cursor-pointer hover:border-blue-500 hover:bg-blue-50",
										)}
										onClick={() => {
											if (!uploadingImage) {
												openDropzone();
											}
										}}
									>
										<input {...getInputProps()} id="upload-image" />
										<div className="h-full space-y-2">
											<SubmitButton
												size="sm"
												variant={uploadingImage ? "ghost" : "default"}
												isSubmitting={uploadingImage}
												className={cn(!uploadingImage && "bg-blue-500 font-normal hover:bg-blue-600")}
											>
												Upload image
											</SubmitButton>
											<div>
												<p className="text-xs text-gray-500">{uploadingImage ? "Uploading..." : "or drop photo here"}</p>
											</div>
										</div>
									</div>
								)}
							</div>

							{/* Prompt输入框 */}
							<div className="space-y-2">
								<Label htmlFor="prompt-input" className="text-sm font-medium text-gray-900">
									Prompt
								</Label>
								<Textarea
									id="prompt-input"
									placeholder="Describe what you want to remove. e.g. Remove all people from the image"
									value={prompt}
									onChange={(e) => setPrompt(e.target.value)}
									className="min-h-[100px] resize-none bg-white focus-visible:border-blue-500 focus-visible:ring-blue-500/50"
									maxLength={500}
								/>
								<div className="flex flex-row flex-wrap items-center gap-1.5">
									{["Remove all people from the image", "Remove the person on the left", "Remove the person in the background"].map(
										(item, index) => (
											<button
												key={index}
												className="rounded-full border border-transparent bg-white px-2 py-0.5 text-xs text-neutral-700 duration-200 hover:scale-105 hover:border-blue-500 hover:text-black"
												onClick={() => setPrompt(item + ", keep the rest unchanged")}
											>
												<p className="">{item}</p>
											</button>
										),
									)}
								</div>
							</div>

							{/* 生成按钮 */}
							<SubmitButton
								isSubmitting={isGenerating}
								onClick={handleGenerate}
								disabled={!prompt.trim() || isGenerating || !uploadedImage}
								className="w-full bg-blue-500 hover:bg-blue-500/80"
								size="lg"
							>
								<SparklesIcon className="mr-1 h-4 w-4" />
								Generate
							</SubmitButton>
						</div>
					)}
				</div>

				{/* 右侧 */}
				<div className="space-y-6">
					{!hasEverUploaded ? (
						// 初始状态 - 上传区域
						<Card
							className={cn(
								"aspect-[3/2] border-[1.5px] border-dashed bg-neutral-50 p-6 shadow-none duration-300",
								!uploadingImage && "hover:border-blue-500 hover:bg-blue-50",
							)}
						>
							<div {...getRootProps()} className="flex h-full flex-col space-y-2">
								<input {...getInputProps()} />

								{/* 上传区域 */}
								<div
									className={cn(
										"flex grow flex-col justify-center p-4 text-center",
										uploadingImage ? "cursor-not-allowed" : "cursor-pointer",
									)}
									onClick={() => {
										if (!uploadingImage) {
											openDropzone();
										}
									}}
								>
									<div className="space-y-2">
										<SubmitButton
											size="sm"
											variant={uploadingImage ? "ghost" : "default"}
											isSubmitting={uploadingImage}
											className={cn(!uploadingImage && "bg-blue-500 font-normal hover:bg-blue-600")}
										>
											Upload image
										</SubmitButton>
										<div>
											<p className="text-xs text-gray-500">{uploadingImage ? "Uploading..." : "or drop photo here"}</p>
										</div>
									</div>
								</div>

								{/* 示例图片 */}
								<div className="flex w-full flex-col items-center space-y-2">
									<div className="via-border mb-2 h-px w-full bg-gradient-to-r from-transparent to-transparent"></div>
									<p className="text-xs text-gray-600">
										<span className="font-medium">No image?</span> Try one of these:
									</p>
									<div className="flex flex-row items-center gap-1.5">
										{exampleData.map((example, index) => (
											<div
												key={index}
												className="border-border/50 ring-offset-background aspect-square size-11 cursor-pointer overflow-hidden rounded-lg border-2 ring-offset-2 transition-all hover:border-blue-500 hover:shadow-lg hover:ring-2 hover:shadow-blue-500/20 hover:ring-blue-500/30"
												// className="aspect-square size-10 cursor-pointer overflow-hidden rounded-md bg-gray-200 transition-all duration-300 hover:scale-110 hover:ring-[1.5px] hover:ring-blue-500"
												onClick={() => handleExampleImageSelect(example.image, example.prompt)}
											>
												<img
													src={example.image}
													alt={`Example ${index + 1}`}
													className="h-full w-full object-cover"
													onError={(e) => {
														// 如果示例图片加载失败，显示占位符
														e.currentTarget.style.display = "none";
													}}
													loading="lazy"
												/>
											</div>
										))}
									</div>
								</div>
							</div>
						</Card>
					) : (
						// 非初始状态 - 生成结果区域
						<Card className="bg-muted h-full min-h-[280px] border-none p-4 shadow-none md:p-6">
							{generatedImage ? (
								<div className="space-y-4">
									<div className="relative">
										<img src={generatedImage} alt="Generated result" className="aspect-square w-full rounded-lg object-cover" />
									</div>

									{/* 操作按钮 */}
									<div className="flex gap-2">
										<Button variant="outline" size="sm" onClick={() => handleUseAsInput(generatedImage)} className="flex-1 font-normal">
											Use as Input
										</Button>

										{userHasPaid ? (
											<Button variant="outline" size="sm" onClick={() => handleDownload(generatedImage)} className="flex-1 font-normal">
												<Download className="mr-2 h-4 w-4" />
												Download
											</Button>
										) : (
											<DropdownMenu>
												<DropdownMenuTrigger asChild>
													<Button variant="outline" size="sm" className="flex-1 font-normal">
														<Download className="mr-2 h-4 w-4" />
														Download
													</Button>
												</DropdownMenuTrigger>
												<DropdownMenuContent>
													<DropdownMenuItem onClick={() => handleDownload(generatedImage)} className="cursor-pointer">
														<DownloadIcon className="text-foreground" />
														Download with watermark
													</DropdownMenuItem>
													<DropdownMenuItem onClick={handleDownloadWithoutWatermark} className="cursor-pointer">
														<CrownIcon className="fill-current text-yellow-500" />
														Download without watermark
													</DropdownMenuItem>
												</DropdownMenuContent>
											</DropdownMenu>
										)}
									</div>
								</div>
							) : (
								<div className="flex h-full items-center justify-center rounded-lg">
									<div className="space-y-2 text-center">
										<div className="mx-auto flex h-16 w-16 items-center justify-center rounded-lg bg-gray-200">
											<SparklesIcon className="h-6 w-6 text-gray-400" />
										</div>
										<p className="text-sm text-gray-500">Generated image will appear here</p>
									</div>
								</div>
							)}
						</Card>
					)}
				</div>
			</div>
		</div>
	);
}
