import { getFileExtension } from "./utils-file";
import { ofetch } from "ofetch";

export const getUploadS3UrlTRPC = async (
	trpcClient: any,
	file: File
): Promise<{ uploadUrl: string; method: string; fileUrl: string }> => {
	// 获取文件扩展名
	const fileExtension = getFileExtension(file.name);
	
	const result = await trpcClient.image.getUploadUrl.mutate({
		fileExtension: fileExtension,
		contentType: file.type,
	});

	return {
		uploadUrl: result.uploadUrl,
		method: result.method,
		fileUrl: result.fileUrl,
	};
};

export const uploadFileTRPC = async (trpcClient: any, file: File): Promise<{ file_url: string }> => {
	const { uploadUrl, method, fileUrl } = await getUploadS3UrlTRPC(trpcClient, file);

	// begin upload
	await ofetch(uploadUrl, {
		method: method,
		headers: {
			accept: "application/json",
		},
		body: file,
	}).catch((error) => {
		throw new Error(error.data?.message || "Failed to upload file.");
	});

	return { file_url: fileUrl };
};
